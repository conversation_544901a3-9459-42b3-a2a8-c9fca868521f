#!/usr/bin/env python3
"""
Comprehensive Test Suite Runner for Enhanced Policy Generation Pipeline

Executes all test categories with coverage reporting and performance analysis:
- Unit tests for individual components
- Integration tests for pipeline workflow
- Performance tests for timing requirements
- Quality validation tests
- Coverage reporting and analysis
"""

import os
import sys
import subprocess
import time
import json
import logging
from pathlib import Path
from typing import Dict, List, Any
from datetime import datetime

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_results.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


class ComprehensiveTestRunner:
    """Comprehensive test suite runner with coverage analysis"""
    
    def __init__(self):
        self.project_root = project_root
        self.test_results = {}
        self.coverage_data = {}
        self.start_time = None
        self.end_time = None
    
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all test categories and generate comprehensive report"""
        
        logger.info("🚀 Starting Comprehensive Test Suite for Enhanced Policy Generation Pipeline")
        self.start_time = time.time()
        
        try:
            # 1. Unit Tests
            logger.info("📋 Running Unit Tests...")
            self.test_results['unit_tests'] = self._run_unit_tests()
            
            # 2. Integration Tests
            logger.info("🔗 Running Integration Tests...")
            self.test_results['integration_tests'] = self._run_integration_tests()
            
            # 3. Performance Tests
            logger.info("⚡ Running Performance Tests...")
            self.test_results['performance_tests'] = self._run_performance_tests()
            
            # 4. Quality Validation Tests
            logger.info("✅ Running Quality Validation Tests...")
            self.test_results['quality_tests'] = self._run_quality_tests()
            
            # 5. Coverage Analysis
            logger.info("📊 Generating Coverage Report...")
            self.coverage_data = self._generate_coverage_report()
            
            self.end_time = time.time()
            
            # 6. Generate Comprehensive Report
            logger.info("📄 Generating Comprehensive Report...")
            report = self._generate_comprehensive_report()
            
            return report
            
        except Exception as e:
            logger.error(f"❌ Test suite execution failed: {e}")
            raise
    
    def _run_unit_tests(self) -> Dict[str, Any]:
        """Run unit tests for individual components"""
        
        unit_test_paths = [
            "tests/backend/services/test_enhanced_policy_generator.py",
            "tests/backend/services/test_policy_quality_validator.py",
            "tests/backend/services/test_variable_substitution.py",
            "tests/backend/services/test_policy_prompt_manager.py"
        ]
        
        results = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'execution_time': 0,
            'details': {}
        }
        
        start_time = time.time()
        
        for test_path in unit_test_paths:
            if os.path.exists(test_path):
                logger.info(f"  Running {test_path}...")
                test_result = self._run_pytest(test_path)
                results['details'][test_path] = test_result
                results['total_tests'] += test_result.get('total', 0)
                results['passed'] += test_result.get('passed', 0)
                results['failed'] += test_result.get('failed', 0)
            else:
                logger.warning(f"  Test file not found: {test_path}")
        
        results['execution_time'] = time.time() - start_time
        
        logger.info(f"  Unit Tests: {results['passed']}/{results['total_tests']} passed in {results['execution_time']:.2f}s")
        return results
    
    def _run_integration_tests(self) -> Dict[str, Any]:
        """Run integration tests for pipeline workflow"""
        
        integration_test_path = "tests/backend/integration/test_enhanced_generation_pipeline.py"
        
        results = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'execution_time': 0,
            'details': {}
        }
        
        start_time = time.time()
        
        if os.path.exists(integration_test_path):
            logger.info(f"  Running {integration_test_path}...")
            test_result = self._run_pytest(integration_test_path)
            results.update(test_result)
            results['details'][integration_test_path] = test_result
        else:
            logger.warning(f"  Integration test file not found: {integration_test_path}")
        
        results['execution_time'] = time.time() - start_time
        
        logger.info(f"  Integration Tests: {results['passed']}/{results['total_tests']} passed in {results['execution_time']:.2f}s")
        return results
    
    def _run_performance_tests(self) -> Dict[str, Any]:
        """Run performance tests for timing requirements"""
        
        performance_test_path = "tests/backend/performance/test_generation_performance.py"
        
        results = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'execution_time': 0,
            'details': {},
            'performance_metrics': {}
        }
        
        start_time = time.time()
        
        if os.path.exists(performance_test_path):
            logger.info(f"  Running {performance_test_path}...")
            test_result = self._run_pytest(performance_test_path, capture_output=True)
            results.update(test_result)
            results['details'][performance_test_path] = test_result
            
            # Extract performance metrics from test output
            results['performance_metrics'] = self._extract_performance_metrics(test_result.get('output', ''))
        else:
            logger.warning(f"  Performance test file not found: {performance_test_path}")
        
        results['execution_time'] = time.time() - start_time
        
        logger.info(f"  Performance Tests: {results['passed']}/{results['total_tests']} passed in {results['execution_time']:.2f}s")
        return results
    
    def _run_quality_tests(self) -> Dict[str, Any]:
        """Run quality validation tests"""
        
        quality_test_path = "tests/backend/quality/test_policy_quality_requirements.py"
        
        results = {
            'total_tests': 0,
            'passed': 0,
            'failed': 0,
            'execution_time': 0,
            'details': {},
            'quality_metrics': {}
        }
        
        start_time = time.time()
        
        if os.path.exists(quality_test_path):
            logger.info(f"  Running {quality_test_path}...")
            test_result = self._run_pytest(quality_test_path, capture_output=True)
            results.update(test_result)
            results['details'][quality_test_path] = test_result
            
            # Extract quality metrics from test output
            results['quality_metrics'] = self._extract_quality_metrics(test_result.get('output', ''))
        else:
            logger.warning(f"  Quality test file not found: {quality_test_path}")
        
        results['execution_time'] = time.time() - start_time
        
        logger.info(f"  Quality Tests: {results['passed']}/{results['total_tests']} passed in {results['execution_time']:.2f}s")
        return results
    
    def _run_pytest(self, test_path: str, capture_output: bool = False) -> Dict[str, Any]:
        """Run pytest on a specific test file"""
        
        cmd = [
            sys.executable, "-m", "pytest", 
            test_path, 
            "-v", 
            "--tb=short",
            "--disable-warnings"
        ]
        
        try:
            result = subprocess.run(
                cmd, 
                cwd=self.project_root,
                capture_output=capture_output,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            # Parse pytest output to extract test counts
            output = result.stdout if capture_output else ""
            
            # Extract test results from pytest output
            passed = output.count(" PASSED")
            failed = output.count(" FAILED")
            total = passed + failed
            
            return {
                'total': total,
                'passed': passed,
                'failed': failed,
                'return_code': result.returncode,
                'output': output if capture_output else ""
            }
            
        except subprocess.TimeoutExpired:
            logger.error(f"  Test {test_path} timed out after 5 minutes")
            return {'total': 0, 'passed': 0, 'failed': 1, 'return_code': -1, 'output': "TIMEOUT"}
        except Exception as e:
            logger.error(f"  Error running test {test_path}: {e}")
            return {'total': 0, 'passed': 0, 'failed': 1, 'return_code': -1, 'output': str(e)}
    
    def _generate_coverage_report(self) -> Dict[str, Any]:
        """Generate code coverage report"""
        
        logger.info("  Generating coverage report...")
        
        # Run tests with coverage
        cmd = [
            sys.executable, "-m", "pytest",
            "tests/backend/services/",
            "--cov=backend/services",
            "--cov-report=json",
            "--cov-report=term-missing",
            "--disable-warnings"
        ]
        
        try:
            result = subprocess.run(
                cmd,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            # Load coverage data
            coverage_file = self.project_root / "coverage.json"
            if coverage_file.exists():
                with open(coverage_file, 'r') as f:
                    coverage_data = json.load(f)
                
                total_coverage = coverage_data.get('totals', {}).get('percent_covered', 0)
                
                return {
                    'total_coverage': total_coverage,
                    'files': coverage_data.get('files', {}),
                    'summary': coverage_data.get('totals', {}),
                    'output': result.stdout
                }
            else:
                logger.warning("  Coverage file not found")
                return {'total_coverage': 0, 'files': {}, 'summary': {}}
                
        except Exception as e:
            logger.error(f"  Error generating coverage report: {e}")
            return {'total_coverage': 0, 'files': {}, 'summary': {}, 'error': str(e)}
    
    def _extract_performance_metrics(self, output: str) -> Dict[str, Any]:
        """Extract performance metrics from test output"""
        
        metrics = {}
        
        # Look for performance-related log messages
        lines = output.split('\n')
        for line in lines:
            if 'Generation Time:' in line:
                try:
                    time_str = line.split('Generation Time:')[1].strip().replace('s', '')
                    metrics['generation_time'] = float(time_str)
                except:
                    pass
            elif 'Memory Increase:' in line:
                try:
                    memory_str = line.split('Memory Increase:')[1].strip().replace('MB', '')
                    metrics['memory_increase'] = float(memory_str)
                except:
                    pass
        
        return metrics
    
    def _extract_quality_metrics(self, output: str) -> Dict[str, Any]:
        """Extract quality metrics from test output"""
        
        metrics = {}
        
        # Look for quality-related log messages
        lines = output.split('\n')
        for line in lines:
            if 'Word Count:' in line:
                try:
                    count_str = line.split('Word Count:')[1].strip().split()[0]
                    metrics['word_count'] = int(count_str)
                except:
                    pass
            elif 'Overall Score:' in line:
                try:
                    score_str = line.split('Overall Score:')[1].strip()
                    metrics['overall_score'] = float(score_str)
                except:
                    pass
        
        return metrics

    def _generate_comprehensive_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""

        total_execution_time = self.end_time - self.start_time

        # Calculate overall statistics
        total_tests = sum(result.get('total_tests', 0) for result in self.test_results.values())
        total_passed = sum(result.get('passed', 0) for result in self.test_results.values())
        total_failed = sum(result.get('failed', 0) for result in self.test_results.values())

        overall_success_rate = (total_passed / total_tests * 100) if total_tests > 0 else 0

        report = {
            'timestamp': datetime.now().isoformat(),
            'execution_time': total_execution_time,
            'summary': {
                'total_tests': total_tests,
                'passed': total_passed,
                'failed': total_failed,
                'success_rate': overall_success_rate
            },
            'test_categories': self.test_results,
            'coverage': self.coverage_data,
            'requirements_compliance': self._assess_requirements_compliance()
        }

        # Save report to file
        report_file = self.project_root / "test_report.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)

        # Generate human-readable summary
        self._print_summary_report(report)

        return report

    def _assess_requirements_compliance(self) -> Dict[str, Any]:
        """Assess compliance with Task 7 requirements"""

        compliance = {
            'unit_tests': False,
            'integration_tests': False,
            'performance_tests': False,
            'quality_tests': False,
            'coverage_target': False,
            'overall_compliance': False
        }

        # Check unit tests
        unit_results = self.test_results.get('unit_tests', {})
        compliance['unit_tests'] = unit_results.get('passed', 0) > 0 and unit_results.get('failed', 0) == 0

        # Check integration tests
        integration_results = self.test_results.get('integration_tests', {})
        compliance['integration_tests'] = integration_results.get('passed', 0) > 0 and integration_results.get('failed', 0) == 0

        # Check performance tests
        performance_results = self.test_results.get('performance_tests', {})
        compliance['performance_tests'] = performance_results.get('passed', 0) > 0 and performance_results.get('failed', 0) == 0

        # Check quality tests
        quality_results = self.test_results.get('quality_tests', {})
        compliance['quality_tests'] = quality_results.get('passed', 0) > 0 and quality_results.get('failed', 0) == 0

        # Check coverage target (>90%)
        total_coverage = self.coverage_data.get('total_coverage', 0)
        compliance['coverage_target'] = total_coverage >= 90.0

        # Overall compliance
        compliance['overall_compliance'] = all([
            compliance['unit_tests'],
            compliance['integration_tests'],
            compliance['performance_tests'],
            compliance['quality_tests'],
            compliance['coverage_target']
        ])

        return compliance

    def _print_summary_report(self, report: Dict[str, Any]):
        """Print human-readable summary report"""

        print("\n" + "="*80)
        print("📋 COMPREHENSIVE TEST SUITE REPORT")
        print("="*80)

        # Summary
        summary = report['summary']
        print(f"\n📊 OVERALL SUMMARY:")
        print(f"   Total Tests: {summary['total_tests']}")
        print(f"   Passed: {summary['passed']} ✅")
        print(f"   Failed: {summary['failed']} ❌")
        print(f"   Success Rate: {summary['success_rate']:.1f}%")
        print(f"   Execution Time: {report['execution_time']:.2f}s")

        # Test Categories
        print(f"\n🔍 TEST CATEGORIES:")
        for category, results in report['test_categories'].items():
            status = "✅" if results.get('failed', 0) == 0 else "❌"
            print(f"   {category.replace('_', ' ').title()}: {results.get('passed', 0)}/{results.get('total_tests', 0)} {status}")

        # Coverage
        coverage = report['coverage']
        coverage_status = "✅" if coverage.get('total_coverage', 0) >= 90 else "❌"
        print(f"\n📈 CODE COVERAGE:")
        print(f"   Total Coverage: {coverage.get('total_coverage', 0):.1f}% {coverage_status}")

        # Requirements Compliance
        compliance = report['requirements_compliance']
        print(f"\n✅ REQUIREMENTS COMPLIANCE:")
        print(f"   Unit Tests: {'✅' if compliance['unit_tests'] else '❌'}")
        print(f"   Integration Tests: {'✅' if compliance['integration_tests'] else '❌'}")
        print(f"   Performance Tests: {'✅' if compliance['performance_tests'] else '❌'}")
        print(f"   Quality Tests: {'✅' if compliance['quality_tests'] else '❌'}")
        print(f"   Coverage Target (>90%): {'✅' if compliance['coverage_target'] else '❌'}")
        print(f"   Overall Compliance: {'✅' if compliance['overall_compliance'] else '❌'}")

        # Performance Metrics
        perf_metrics = report['test_categories'].get('performance_tests', {}).get('performance_metrics', {})
        if perf_metrics:
            print(f"\n⚡ PERFORMANCE METRICS:")
            if 'generation_time' in perf_metrics:
                time_status = "✅" if perf_metrics['generation_time'] < 120 else "❌"
                print(f"   Generation Time: {perf_metrics['generation_time']:.2f}s (target: <120s) {time_status}")
            if 'memory_increase' in perf_metrics:
                memory_status = "✅" if perf_metrics['memory_increase'] < 500 else "❌"
                print(f"   Memory Increase: {perf_metrics['memory_increase']:.2f}MB (target: <500MB) {memory_status}")

        # Quality Metrics
        quality_metrics = report['test_categories'].get('quality_tests', {}).get('quality_metrics', {})
        if quality_metrics:
            print(f"\n🏆 QUALITY METRICS:")
            if 'word_count' in quality_metrics:
                word_status = "✅" if quality_metrics['word_count'] >= 12000 else "❌"
                print(f"   Total Words: {quality_metrics['word_count']} (target: ≥12,000) {word_status}")
            if 'overall_score' in quality_metrics:
                score_status = "✅" if quality_metrics['overall_score'] >= 0.90 else "❌"
                print(f"   Quality Score: {quality_metrics['overall_score']:.3f} (target: ≥0.90) {score_status}")

        print("\n" + "="*80)

        # Final status
        if compliance['overall_compliance']:
            print("🎉 ALL REQUIREMENTS MET - TASK 7 COMPLETE!")
        else:
            print("⚠️  SOME REQUIREMENTS NOT MET - REVIEW NEEDED")

        print("="*80)


def main():
    """Main entry point for comprehensive test suite"""

    try:
        runner = ComprehensiveTestRunner()
        report = runner.run_all_tests()

        # Exit with appropriate code
        if report['requirements_compliance']['overall_compliance']:
            sys.exit(0)  # Success
        else:
            sys.exit(1)  # Failure

    except Exception as e:
        logger.error(f"❌ Test suite failed with error: {e}")
        sys.exit(2)  # Error


if __name__ == "__main__":
    main()
