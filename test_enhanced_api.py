#!/usr/bin/env python3
"""
Test the Enhanced Policy Generation API
Makes real HTTP requests to the running server to generate a full policy document
"""

import requests
import json
import time
from datetime import datetime

# API Configuration
BASE_URL = "http://localhost:8000"
API_ENDPOINT = "/api/enhanced-policies/api/policies/generate-enhanced"

def authenticate_user():
    """Authenticate and get a token"""
    print("🔐 Authenticating user...")

    # First, register a test user
    register_data = {
        "fullName": "Test User",
        "companyName": "TechSecure Solutions Ltd",
        "email": "<EMAIL>",
        "password": "TestPassword123!"
    }

    try:
        # Try to register (might fail if user exists)
        register_response = requests.post(f"{BASE_URL}/api/auth/signup", json=register_data)
        if register_response.status_code == 200:
            print("✅ User registered successfully")
            response_data = register_response.json()
            if "token" in response_data:
                print("✅ Authentication successful")
                return response_data["token"]
        elif register_response.status_code == 400:
            print("ℹ️  User already exists, proceeding to login")
        else:
            print(f"⚠️  Registration response: {register_response.status_code}")
            print(f"Response: {register_response.text}")
    except Exception as e:
        print(f"⚠️  Registration attempt: {e}")

    # Login to get token
    login_data = {
        "email": "<EMAIL>",
        "password": "TestPassword123!"
    }

    try:
        login_response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        if login_response.status_code == 200:
            response_data = login_response.json()
            token = response_data.get("token")
            print("✅ Authentication successful")
            return token
        else:
            print(f"❌ Login failed: {login_response.status_code}")
            print(f"Response: {login_response.text}")
            return None
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None

def generate_enhanced_policy(token):
    """Generate a comprehensive policy using the enhanced API"""
    
    print("\n🚀 Starting Enhanced Policy Generation")
    print("=" * 60)
    
    # Comprehensive policy request
    policy_request = {
        "framework": "GDPR",
        "company_name": "TechSecure Solutions Ltd",
        "company_type": "Technology Company",
        "industry": "Software Development & Cloud Services",
        "employee_count": 150,
        "data_types": [
            "personal_data",
            "financial_data", 
            "customer_data",
            "employee_data",
            "technical_data",
            "behavioral_data"
        ],
        "existing_policies": [
            "Privacy Policy",
            "Data Retention Policy",
            "Security Policy",
            "Employee Handbook"
        ],
        "complexity_level": "comprehensive",
        "jurisdiction": "UK",
        "risk_profile": "high",
        "generation_mode": "enhanced"
    }
    
    print(f"📋 Policy Request Details:")
    print(f"   Company: {policy_request['company_name']}")
    print(f"   Framework: {policy_request['framework']}")
    print(f"   Industry: {policy_request['industry']}")
    print(f"   Employees: {policy_request['employee_count']}")
    print(f"   Data Types: {len(policy_request['data_types'])} types")
    print(f"   Complexity: {policy_request['complexity_level']}")
    print(f"   Risk Profile: {policy_request['risk_profile']}")
    print(f"   Mode: {policy_request['generation_mode']}")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    print(f"\n⏳ Sending request to {BASE_URL}{API_ENDPOINT}")
    start_time = time.time()
    
    try:
        response = requests.post(
            f"{BASE_URL}{API_ENDPOINT}",
            json=policy_request,
            headers=headers,
            timeout=300  # 5 minute timeout
        )
        
        end_time = time.time()
        generation_time = end_time - start_time
        
        print(f"📡 Response received in {generation_time:.2f} seconds")
        print(f"📊 Status Code: {response.status_code}")
        
        if response.status_code == 200:
            policy_data = response.json()
            print("✅ Policy generation successful!")
            
            # Display policy metadata
            print(f"\n📊 POLICY METADATA")
            print("-" * 40)
            print(f"Policy ID: {policy_data.get('policy_id', 'N/A')}")
            print(f"Title: {policy_data.get('title', 'N/A')}")
            print(f"Framework: {policy_data.get('framework', 'N/A')}")
            print(f"Generation Mode: {policy_data.get('generation_mode', 'N/A')}")
            
            # Display metadata if available
            metadata = policy_data.get('metadata', {})
            if metadata:
                print(f"Total Words: {metadata.get('total_words', 'N/A'):,}")
                print(f"Total Pages: {metadata.get('total_pages', 'N/A')}")
                print(f"Generation Time: {metadata.get('generation_time', 'N/A'):.2f}s")
            
            # Display quality metrics if available
            quality_metrics = policy_data.get('quality_metrics', {})
            if quality_metrics:
                print(f"\n🏆 QUALITY METRICS")
                print("-" * 40)
                for metric, value in quality_metrics.items():
                    if isinstance(value, (int, float)):
                        print(f"{metric.replace('_', ' ').title()}: {value:.3f}")
                    else:
                        print(f"{metric.replace('_', ' ').title()}: {value}")
            
            # Display the policy content
            content = policy_data.get('content', '')
            if content:
                print(f"\n📄 FULL POLICY DOCUMENT")
                print("=" * 80)
                print(content)
                print("=" * 80)
            
            # Save the policy to a file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"enhanced_policy_{timestamp}.json"
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(policy_data, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Policy saved to: {filename}")
            
            # Display summary
            print(f"\n📈 GENERATION SUMMARY")
            print("-" * 40)
            print(f"✅ Successfully generated {policy_request['framework']} policy")
            print(f"📊 API Response Time: {generation_time:.2f} seconds")
            print(f"📄 Content Length: {len(content):,} characters")
            print(f"💾 Saved to: {filename}")
            
            return policy_data
            
        else:
            print(f"❌ Policy generation failed!")
            print(f"Status Code: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except requests.exceptions.Timeout:
        print("❌ Request timed out after 5 minutes")
        return None
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the server running on localhost:8000?")
        return None
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return None

def main():
    """Main function to test the enhanced API"""
    
    print("🧪 Enhanced Policy Generation API Test")
    print("=" * 60)
    
    # Check if server is running
    try:
        health_response = requests.get(f"{BASE_URL}/health", timeout=5)
        if health_response.status_code == 200:
            print("✅ Server is running and healthy")
        else:
            print(f"⚠️  Server health check returned: {health_response.status_code}")
    except Exception as e:
        print(f"❌ Server not accessible: {e}")
        print("Please ensure the server is running with: uvicorn backend.server:app --host 0.0.0.0 --port 8000")
        return
    
    # Authenticate
    token = authenticate_user()
    if not token:
        print("❌ Authentication failed - cannot proceed")
        return
    
    # Generate policy
    policy_data = generate_enhanced_policy(token)
    
    if policy_data:
        print("\n🎉 Enhanced Policy Generation Test Completed Successfully!")
        print("The generated policy demonstrates the full capabilities of the enhanced API.")
    else:
        print("\n❌ Enhanced Policy Generation Test Failed!")

if __name__ == "__main__":
    main()
