#!/usr/bin/env python3
"""
Generate a sample policy document using the Enhanced Policy Generation API
This script demonstrates the full enhanced policy generation capabilities
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# Add project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from backend.services.enhanced_policy_generator import (
    EnhancedPolicyGenerator,
    PolicyRequest,
    AIService
)
from backend.config import GEMINI_API_KEY
from motor.motor_asyncio import AsyncIOMotorClient
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def generate_sample_policy():
    """Generate a comprehensive sample policy document"""
    
    print("🚀 Starting Enhanced Policy Generation Demo")
    print("=" * 60)
    
    # Initialize AI service
    if not GEMINI_API_KEY:
        print("❌ GEMINI_API_KEY not found. Please set your API key in backend/.env")
        return
    
    ai_service = AIService(GEMINI_API_KEY)
    
    # Initialize database connection
    client = AsyncIOMotorClient("mongodb://localhost:27017")
    db = client.compliance_gpt
    
    # Initialize enhanced policy generator
    generator = EnhancedPolicyGenerator(db=db, ai_service=ai_service)
    
    # Create a comprehensive policy request
    policy_request = PolicyRequest(
        user_id="demo_user_123",
        company_name="TechSecure Solutions Ltd",
        company_type="Technology Company",
        industry="Software Development & Cloud Services",
        employee_count=150,
        framework="gdpr",
        data_types=[
            "personal_data", 
            "financial_data", 
            "customer_data", 
            "employee_data",
            "technical_data",
            "behavioral_data"
        ],
        existing_policies=[
            "Privacy Policy",
            "Data Retention Policy", 
            "Security Policy",
            "Employee Handbook"
        ],
        complexity_level="comprehensive",
        jurisdiction="UK",
        risk_profile="high"
    )
    
    print(f"📋 Generating policy for: {policy_request.company_name}")
    print(f"🏢 Company Type: {policy_request.company_type}")
    print(f"🏭 Industry: {policy_request.industry}")
    print(f"👥 Employee Count: {policy_request.employee_count}")
    print(f"📊 Framework: {policy_request.framework.upper()}")
    print(f"🌍 Jurisdiction: {policy_request.jurisdiction}")
    print(f"⚠️  Risk Profile: {policy_request.risk_profile}")
    print(f"📈 Complexity: {policy_request.complexity_level}")
    print(f"📁 Data Types: {', '.join(policy_request.data_types)}")
    print(f"📄 Existing Policies: {', '.join(policy_request.existing_policies)}")
    print()
    
    try:
        # Generate the comprehensive policy
        start_time = datetime.now()
        print("⏳ Starting policy generation...")
        
        policy_document = await generator.generate_comprehensive_policy(policy_request)
        
        end_time = datetime.now()
        generation_time = (end_time - start_time).total_seconds()
        
        if not policy_document:
            print("❌ Policy generation failed!")
            return
        
        print(f"✅ Policy generation completed in {generation_time:.2f} seconds!")
        print()
        
        # Display policy metadata
        print("📊 POLICY METADATA")
        print("-" * 40)
        print(f"Policy ID: {policy_document.policy_id}")
        print(f"Total Words: {policy_document.metadata.get('total_words', 'N/A'):,}")
        print(f"Total Pages: {policy_document.metadata.get('total_pages', 'N/A')}")
        print(f"Generation Time: {policy_document.metadata.get('generation_time', 'N/A'):.2f}s")
        print(f"Overall Quality Score: {policy_document.quality_metrics.get('overall_score', 'N/A'):.3f}")
        print()
        
        # Display quality metrics for each stage
        print("🏆 QUALITY METRICS BY STAGE")
        print("-" * 40)
        for stage_name, stage_output in policy_document.content.items():
            quality_score = stage_output.completeness_score
            word_count = stage_output.word_count
            print(f"{stage_name.replace('_', ' ').title()}: {quality_score:.3f} ({word_count:,} words)")
        print()
        
        # Display each section of the policy
        print("📄 FULL POLICY DOCUMENT")
        print("=" * 80)
        
        for stage_name, stage_output in policy_document.content.items():
            print(f"\n{'='*20} {stage_name.replace('_', ' ').upper()} {'='*20}")
            print(f"Word Count: {stage_output.word_count:,}")
            print(f"Quality Score: {stage_output.completeness_score:.3f}")
            print(f"Generation Time: {stage_output.generation_time:.2f}s")
            print("-" * 60)
            print(stage_output.content)
            print()
        
        # Save the policy to a file
        output_filename = f"sample_policy_{policy_document.policy_id[:8]}.json"
        
        # Prepare policy data for JSON serialization
        policy_data = {
            "policy_id": policy_document.policy_id,
            "metadata": policy_document.metadata,
            "quality_metrics": policy_document.quality_metrics,
            "generation_config": policy_document.generation_config,
            "content": {}
        }
        
        # Convert StageOutput objects to dictionaries
        for stage_name, stage_output in policy_document.content.items():
            policy_data["content"][stage_name] = {
                "content": stage_output.content,
                "word_count": stage_output.word_count,
                "completeness_score": stage_output.completeness_score,
                "generation_time": stage_output.generation_time,
                "metadata": stage_output.metadata
            }
        
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(policy_data, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Policy saved to: {output_filename}")
        
        # Display summary statistics
        print("\n📈 GENERATION SUMMARY")
        print("-" * 40)
        print(f"✅ Successfully generated comprehensive {policy_request.framework.upper()} policy")
        print(f"📊 Total content: {policy_document.metadata.get('total_words', 0):,} words across {len(policy_document.content)} sections")
        print(f"⏱️  Generation time: {generation_time:.2f} seconds")
        print(f"🏆 Overall quality: {policy_document.quality_metrics.get('overall_score', 0):.1%}")
        print(f"💾 Saved to: {output_filename}")
        
        # Close database connection
        client.close()
        
        return policy_document
        
    except Exception as e:
        print(f"❌ Error during policy generation: {e}")
        logger.exception("Policy generation failed")
        return None

async def main():
    """Main entry point"""
    try:
        policy_document = await generate_sample_policy()
        if policy_document:
            print("\n🎉 Enhanced Policy Generation Demo Completed Successfully!")
        else:
            print("\n❌ Enhanced Policy Generation Demo Failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️  Generation interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
