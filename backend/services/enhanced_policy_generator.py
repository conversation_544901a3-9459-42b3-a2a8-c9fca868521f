"""
Enhanced Policy Generator - Multi-Stage AI Pipeline for Enterprise-Grade Policies
Transforms ComplianceGPT from demo-quality outlines to comprehensive 20+ page policies
"""

import os
import asyncio
import json
import uuid
import time
import math
from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
import logging
from dataclasses import dataclass
from enum import Enum

import google.generativeai as genai
from motor.motor_asyncio import AsyncIOMotorDatabase
from langtrace_python_sdk import langtrace, with_langtrace_root_span
from .policy_quality_validator import PolicyQualityValidator # Added import
from .policy_models import GenerationStage, PolicyRequest, StageOutput, PolicyDocument # Import shared models

# Enhanced Infrastructure Imports
from ..exceptions.enhanced_policy_exceptions import (
    EnhancedPolicyException, AIServiceException, ValidationException,
    ConfigurationException, DatabaseException, TimeoutException,
    RateLimitException, ContentException, AssemblyException,
    QualityException, create_exception # Corrected import
)
from ..utils.retry_utils import (
    with_retry, with_retry_sync, with_retry_config, ExponentialBackoffRetry,
    AI_SERVICE_RETRY_CONFIG, QUICK_RETRY_CONFIG, AGGRESSIVE_RETRY_CONFIG
)
from ..utils.circuit_breaker import (
    with_circuit_breaker, CircuitBreaker, get_circuit_breaker,
    create_ai_service_circuit_breaker, create_quick_circuit_breaker
)
from ..utils.logging_config import (
    get_service_logger, correlation_context, performance_timer,
    PolicyGeneratorLogAdapter, StructuredJSONFormatter
)
from ..utils.error_recovery import (
    with_recovery, get_recovery_manager, ErrorRecoveryManager,
    RecoveryContext, RecoveryAction, RecoveryStrategy
)
from ..utils.metrics_collector import metrics_collector, StageType
from .variable_substitution import variable_engine, VariableValidationError

# Enhanced logger with structured JSON formatting and correlation support
logger = get_service_logger()

class PolicyPromptManager:
    """Manages specialized prompts for each generation stage with unified variable substitution"""

    _PROMPT_TEMPLATE_DIR = os.path.join(os.path.dirname(__file__), "..", "prompts", "templates")

    def __init__(self):
        self.FRAMEWORK_PROMPT = self._load_prompt("framework_prompt.txt")
        self.PROCEDURES_PROMPT = self._load_prompt("procedures_prompt.txt")
        self.TOOLS_PROMPT = self._load_prompt("tools_prompt.txt")
        self.ASSEMBLY_PROMPT = self._load_prompt("assembly_prompt.txt")

        # Initialize variable substitution engine
        self.variable_engine = variable_engine

    def _load_prompt(self, filename: str) -> str:
        """Loads a prompt template from the specified file."""
        file_path = os.path.join(self._PROMPT_TEMPLATE_DIR, filename)
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except FileNotFoundError:
            # In a real scenario, you'd want robust logging here
            # For now, raising an error might be best to catch issues early
            # Or, return a default error message string if that's preferable
            # For enterprise gold standard, proper logging and error handling is key.
            # Consider using the logging module configured for the application.
            # For this iteration, let's print a warning and return an empty string
            # to allow tests to potentially catch this, but this should be improved.
            print(f"WARNING: Prompt file not found: {file_path}. Returning empty prompt.")
            # Consider: logging.error(f"Prompt file not found: {file_path}")
            # Consider: raise FileNotFoundError(f"Prompt file {file_path} not found.")
            return "" # Or a default error prompt string

    # The rest of the class (get_framework_prompt, etc.) remains the same
    # but the large prompt string definitions (FRAMEWORK_PROMPT, etc.) are removed from here.

    # Original FRAMEWORK_PROMPT content is now in framework_prompt.txt
    # Original PROCEDURES_PROMPT content is now in procedures_prompt.txt
    # Original TOOLS_PROMPT content is now in tools_prompt.txt

    # Retain the methods that use these prompts:
    def get_framework_prompt(self, request: PolicyRequest, data_context: Optional[StageOutput] = None, risk_context: Optional[StageOutput] = None) -> str:
        """Formats the framework generation prompt with request-specific details and context."""
        try:
            # Prepare variable values using the unified engine
            variable_values = self.variable_engine.prepare_variable_values(
                request=request,
                data_context=data_context,
                risk_context=risk_context
            )

            # Perform variable substitution
            return self.variable_engine.substitute_variables(self.FRAMEWORK_PROMPT, variable_values)

        except VariableValidationError as e:
            logger.error("Variable validation failed in get_framework_prompt: %s", str(e))
            # Fallback to original method for backward compatibility
            return self.FRAMEWORK_PROMPT.format(
                company_name=request.company_name,
                company_type=request.company_type,
                industry=request.industry,
                employee_count=request.employee_count,
                data_types=", ".join(request.data_types),
                jurisdiction=request.jurisdiction,
                risk_profile=request.risk_profile,
                framework=request.framework,
                data_context=data_context.content if data_context else "No data context provided.",
                risk_context=risk_context.content if risk_context else "No risk context provided."
            )

    def get_procedures_prompt(self, framework_output: StageOutput, request: PolicyRequest, data_context: Optional[StageOutput] = None, risk_context: Optional[StageOutput] = None) -> str:
        """Formats the procedures generation prompt."""
        try:
            # Prepare variable values using the unified engine
            variable_values = self.variable_engine.prepare_variable_values(
                request=request,
                framework_output=framework_output,
                data_context=data_context,
                risk_context=risk_context
            )

            # Perform variable substitution
            return self.variable_engine.substitute_variables(self.PROCEDURES_PROMPT, variable_values)

        except VariableValidationError as e:
            logger.error("Variable validation failed in get_procedures_prompt: %s", str(e))
            # Fallback to original method for backward compatibility
            return self.PROCEDURES_PROMPT.format(
                company_name=request.company_name,
                company_type=request.company_type,
                industry=request.industry,
                employee_count=request.employee_count,
                data_types=", ".join(request.data_types),
                existing_policies=", ".join(request.existing_policies),
                complexity_level=request.complexity_level,
                jurisdiction=request.jurisdiction,
                risk_profile=request.risk_profile,
                framework=request.framework,
                framework_content=framework_output.content,
                data_context=data_context.content if data_context else "No data context provided.",
                risk_context=risk_context.content if risk_context else "No risk context provided."
            )

    def get_tools_prompt(self, framework_output: StageOutput, procedures_output: StageOutput, request: PolicyRequest, data_context: Optional[StageOutput] = None, risk_context: Optional[StageOutput] = None) -> str:
        """Formats the tools and technologies generation prompt."""
        try:
            # Prepare variable values using the unified engine
            variable_values = self.variable_engine.prepare_variable_values(
                request=request,
                framework_output=framework_output,
                procedures_output=procedures_output,
                data_context=data_context,
                risk_context=risk_context
            )

            # Perform variable substitution
            return self.variable_engine.substitute_variables(self.TOOLS_PROMPT, variable_values)

        except VariableValidationError as e:
            logger.error("Variable validation failed in get_tools_prompt: %s", str(e))
            # Fallback to original method for backward compatibility
            company_context_details = [
                f"Company Name: {request.company_name}",
                f"Type: {request.company_type}",
                f"Industry: {request.industry}",
                f"Employees: {request.employee_count}",
                f"Jurisdiction: {request.jurisdiction}",
                f"Risk Profile: {request.risk_profile}",
                f"Processes Data Types: {', '.join(request.data_types)}",
                f"Existing Policies: {', '.join(request.existing_policies)}",
                f"Policy Complexity: {request.complexity_level}"
            ]
            company_context = ". ".join(filter(None, company_context_details)) + "."

            return self.TOOLS_PROMPT.format(
                company_name=request.company_name,
                company_type=request.company_type,
                industry=request.industry,
                employee_count=request.employee_count,
                data_types=", ".join(request.data_types),
                existing_policies=", ".join(request.existing_policies),
                complexity_level=request.complexity_level,
                jurisdiction=request.jurisdiction,
                risk_profile=request.risk_profile,
                framework=request.framework,
                framework_content=framework_output.content,
                procedures_content=procedures_output.content,
                company_context=company_context,
                data_context=data_context.content if data_context else "No data context provided.",
                risk_context=risk_context.content if risk_context else "No risk context provided."
            )
    def get_data_collection_prompt(self, request: PolicyRequest, analysis_type: str) -> str:
        """Generates comprehensive data collection prompts for organizational, regulatory, or industry analysis."""

        base_context = f"""
SYSTEM INSTRUCTION:
You are a senior compliance consultant and data analyst specializing in {request.framework} compliance.
Your task is to conduct comprehensive {analysis_type} data collection and analysis for compliance policy development.

COMPANY CONTEXT:
- Company Name: {request.company_name}
- Company Type: {request.company_type}
- Industry: {request.industry}
- Employee Count: {request.employee_count}
- Data Types Processed: {', '.join(request.data_types)}
- Jurisdiction: {request.jurisdiction}
- Risk Profile: {request.risk_profile}
- Compliance Framework: {request.framework}
"""

        if analysis_type == "organizational":
            return base_context + """
DATA COLLECTION REQUIREMENTS - ORGANIZATIONAL ANALYSIS:

**SECTION 1: ORGANIZATIONAL STRUCTURE & GOVERNANCE**
- Complete organizational chart with data handling roles and responsibilities
- Board-level data protection governance structure and oversight mechanisms
- Data Protection Officer (DPO) appointment status and qualifications
- Privacy steering committee composition and meeting frequency
- Reporting lines for data protection matters to senior management

**SECTION 2: BUSINESS OPERATIONS & DATA FLOWS**
- Detailed business process mapping with data touchpoints
- Customer journey mapping with data collection and processing points
- Employee data handling procedures and access controls
- Third-party vendor relationships and data sharing agreements
- International data transfer mechanisms and safeguards

**SECTION 3: TECHNOLOGY INFRASTRUCTURE**
- IT systems inventory with data processing capabilities
- Cloud services usage and data residency arrangements
- Database architecture and data storage locations
- Security controls and access management systems
- Data backup and disaster recovery procedures

**SECTION 4: CURRENT COMPLIANCE POSTURE**
- Existing privacy policies and procedures documentation
- Previous compliance assessments and audit findings
- Data breach incident history and response procedures
- Training programs and staff awareness levels
- Budget allocation for compliance initiatives

TARGET OUTPUT: Comprehensive organizational profile (2,000-3,000 words)
FOCUS: Actionable insights for policy development and compliance gap identification.

ANALYSIS TASK: Conduct thorough organizational analysis based on the company context above.
"""

        elif analysis_type == "regulatory":
            return base_context + """
DATA COLLECTION REQUIREMENTS - REGULATORY ENVIRONMENT ANALYSIS:

**SECTION 1: PRIMARY REGULATORY FRAMEWORK**
- Complete {request.framework} requirements mapping for {request.industry} sector
- Specific articles, clauses, and provisions applicable to {request.company_name}
- Enforcement actions and penalties relevant to company size and operations
- Recent regulatory guidance and interpretations affecting the industry

**SECTION 2: JURISDICTIONAL COMPLIANCE MATRIX**
- {request.jurisdiction} specific data protection laws and regulations
- Cross-border data transfer requirements and mechanisms
- Sector-specific regulations (financial services, healthcare, etc.)
- Local data residency and localization requirements

**SECTION 3: REGULATORY AUTHORITY LANDSCAPE**
- Primary supervisory authority identification and contact information
- Notification and registration requirements with regulatory bodies
- Reporting obligations and submission deadlines
- Enforcement trends and penalty patterns for similar organizations

**SECTION 4: INDUSTRY STANDARDS & BEST PRACTICES**
- Industry-specific compliance frameworks and certifications
- Professional body guidelines and recommendations
- Peer organization compliance approaches and lessons learned
- Emerging regulatory trends and proposed legislative changes

**SECTION 5: COMPLIANCE OBLIGATIONS CALENDAR**
- Annual compliance reporting requirements and deadlines
- Periodic review and update obligations for policies and procedures
- Training and awareness program requirements
- Audit and assessment scheduling requirements

TARGET OUTPUT: Comprehensive regulatory landscape analysis (2,500-3,500 words)
FOCUS: Complete regulatory compliance roadmap with specific obligations and timelines.

ANALYSIS TASK: Conduct detailed regulatory environment analysis for {request.framework} compliance.
"""

        elif analysis_type == "industry":
            return base_context + """
DATA COLLECTION REQUIREMENTS - INDUSTRY CONTEXT ANALYSIS:

**SECTION 1: INDUSTRY DATA PROCESSING PATTERNS**
- Common data types processed in {request.industry} sector
- Typical data collection methods and customer touchpoints
- Industry-standard data retention periods and deletion practices
- Sector-specific data sharing and collaboration requirements

**SECTION 2: TECHNOLOGY LANDSCAPE & TRENDS**
- Prevalent technology platforms and systems in {request.industry}
- Cloud adoption patterns and preferred service providers
- Emerging technologies (AI, IoT, blockchain) and privacy implications
- Cybersecurity threats and vulnerabilities specific to the sector

**SECTION 3: COMPETITIVE COMPLIANCE LANDSCAPE**
- Industry leaders' privacy program maturity and best practices
- Common compliance challenges and solution approaches
- Regulatory enforcement patterns and case studies in the sector
- Industry association guidelines and collaborative initiatives

**SECTION 4: CUSTOMER & STAKEHOLDER EXPECTATIONS**
- Consumer privacy expectations and preferences in {request.industry}
- Business partner and vendor compliance requirements
- Investor and stakeholder privacy governance expectations
- Public relations and brand reputation considerations

**SECTION 5: OPERATIONAL RISK FACTORS**
- Industry-specific data breach risks and attack vectors
- Supply chain vulnerabilities and third-party dependencies
- Seasonal or cyclical compliance challenges
- Economic factors affecting compliance investment and priorities

TARGET OUTPUT: Comprehensive industry context analysis (2,000-3,000 words)
FOCUS: Industry-specific insights for tailored compliance strategy development.

ANALYSIS TASK: Conduct thorough industry context analysis for {request.industry} sector compliance.
"""

        else:
            # Fallback for any other analysis types
            return base_context + f"""
DATA COLLECTION REQUIREMENTS - {analysis_type.upper()} ANALYSIS:

**COMPREHENSIVE DATA COLLECTION**
- Conduct detailed {analysis_type} analysis for {request.company_name}
- Focus on {request.framework} compliance requirements
- Consider {request.industry} sector-specific factors
- Address {request.jurisdiction} regulatory environment
- Account for {request.risk_profile} risk profile

TARGET OUTPUT: Detailed {analysis_type} analysis (2,000-3,000 words)
FOCUS: Actionable insights for compliance policy development.

ANALYSIS TASK: Conduct comprehensive {analysis_type} analysis based on company context.
"""

    def get_risk_assessment_prompt(self, request: PolicyRequest, collected_data: str, risk_type: str) -> str:
        """Generates comprehensive risk assessment prompts based on collected data and risk type."""

        base_context = f"""
SYSTEM INSTRUCTION:
You are a senior risk assessment specialist and compliance expert with expertise in {request.framework} compliance.
Your task is to conduct comprehensive {risk_type} risk assessment based on collected organizational data.

COMPANY CONTEXT:
- Company Name: {request.company_name}
- Company Type: {request.company_type}
- Industry: {request.industry}
- Employee Count: {request.employee_count}
- Data Types Processed: {', '.join(request.data_types)}
- Jurisdiction: {request.jurisdiction}
- Risk Profile: {request.risk_profile}
- Compliance Framework: {request.framework}

COLLECTED DATA FOR ANALYSIS:
{collected_data}
"""

        if risk_type == "technical":
            return base_context + """
RISK ASSESSMENT REQUIREMENTS - TECHNICAL RISK ANALYSIS:

**SECTION 1: INFORMATION SECURITY RISK ASSESSMENT**
- Evaluate technical safeguards and security controls effectiveness
- Assess data encryption implementation (at rest, in transit, in processing)
- Analyze access control mechanisms and authentication systems
- Review network security architecture and segmentation
- Evaluate backup and disaster recovery technical capabilities

**SECTION 2: SYSTEM VULNERABILITY ANALYSIS**
- Identify potential technical vulnerabilities in data processing systems
- Assess cloud infrastructure security and configuration risks
- Evaluate third-party integration security and API vulnerabilities
- Analyze database security controls and access logging
- Review mobile and remote access security measures

**SECTION 3: DATA PROCESSING TECHNOLOGY RISKS**
- Evaluate automated decision-making and profiling systems
- Assess AI/ML model privacy and bias risks
- Analyze data analytics and reporting system privacy controls
- Review data integration and ETL process security
- Evaluate real-time processing and streaming data risks

**SECTION 4: TECHNICAL COMPLIANCE GAPS**
- Identify gaps in technical measures required by {request.framework}
- Assess privacy-by-design implementation in technical systems
- Evaluate data subject rights fulfillment technical capabilities
- Analyze breach detection and notification technical systems
- Review audit logging and monitoring technical capabilities

**SECTION 5: EMERGING TECHNOLOGY RISKS**
- Assess risks from new technology adoption and implementation
- Evaluate IoT device security and data collection risks
- Analyze cloud migration and hybrid infrastructure risks
- Review API security and microservices architecture risks
- Assess quantum computing and post-quantum cryptography readiness

RISK SCORING CRITERIA:
- Likelihood: Very Low (1) to Very High (5)
- Impact: Minimal (1) to Catastrophic (5)
- Overall Risk: Low (1-6), Medium (7-15), High (16-25)

TARGET OUTPUT: Comprehensive technical risk assessment (3,000-4,000 words)
FOCUS: Actionable technical risk mitigation recommendations with implementation priorities.

RISK ASSESSMENT TASK: Conduct detailed technical risk analysis based on collected data.
"""

        elif risk_type == "operational":
            return base_context + """
RISK ASSESSMENT REQUIREMENTS - OPERATIONAL RISK ANALYSIS:

**SECTION 1: PROCESS AND PROCEDURE RISKS**
- Evaluate data handling process effectiveness and compliance gaps
- Assess staff training and awareness program adequacy
- Analyze incident response and breach management procedures
- Review data subject rights handling operational procedures
- Evaluate vendor management and third-party oversight processes

**SECTION 2: ORGANIZATIONAL GOVERNANCE RISKS**
- Assess data protection governance structure effectiveness
- Evaluate privacy program oversight and accountability mechanisms
- Analyze resource allocation and budget adequacy for compliance
- Review policy and procedure maintenance and update processes
- Assess cross-functional coordination and communication effectiveness

**SECTION 3: HUMAN RESOURCE AND STAFFING RISKS**
- Evaluate staff competency and training in data protection matters
- Assess key person dependencies and succession planning
- Analyze contractor and temporary staff data access controls
- Review background checking and security clearance procedures
- Evaluate performance management and compliance accountability

**SECTION 4: BUSINESS CONTINUITY AND OPERATIONAL RESILIENCE**
- Assess business continuity planning for data protection compliance
- Evaluate disaster recovery procedures and data availability
- Analyze operational resilience during crisis or emergency situations
- Review change management processes for compliance impact assessment
- Assess merger, acquisition, and divestiture compliance procedures

**SECTION 5: THIRD-PARTY AND SUPPLY CHAIN RISKS**
- Evaluate vendor due diligence and onboarding procedures
- Assess data processing agreement management and monitoring
- Analyze supply chain data protection compliance oversight
- Review international data transfer operational controls
- Evaluate service provider performance monitoring and compliance verification

RISK SCORING CRITERIA:
- Likelihood: Very Low (1) to Very High (5)
- Impact: Minimal (1) to Catastrophic (5)
- Overall Risk: Low (1-6), Medium (7-15), High (16-25)

TARGET OUTPUT: Comprehensive operational risk assessment (3,000-4,000 words)
FOCUS: Operational risk mitigation strategies with clear implementation roadmap.

RISK ASSESSMENT TASK: Conduct detailed operational risk analysis based on collected data.
"""

        elif risk_type == "compliance":
            return base_context + """
RISK ASSESSMENT REQUIREMENTS - COMPLIANCE RISK ANALYSIS:

**SECTION 1: REGULATORY COMPLIANCE RISK ASSESSMENT**
- Evaluate current compliance posture against {request.framework} requirements
- Assess regulatory reporting and notification compliance risks
- Analyze cross-border data transfer compliance and adequacy decisions
- Review sector-specific regulatory compliance requirements
- Evaluate regulatory change management and adaptation procedures

**SECTION 2: LEGAL AND CONTRACTUAL RISK ANALYSIS**
- Assess data processing agreement compliance and enforceability
- Evaluate privacy notice and consent mechanism legal adequacy
- Analyze data subject rights response legal compliance
- Review employment law compliance for employee data processing
- Assess intellectual property and confidentiality agreement compliance

**SECTION 3: ENFORCEMENT AND PENALTY RISK EVALUATION**
- Analyze potential regulatory enforcement actions and penalties
- Evaluate litigation risk from data protection violations
- Assess reputational damage risk from compliance failures
- Review insurance coverage adequacy for data protection liabilities
- Analyze financial impact of potential compliance violations

**SECTION 4: AUDIT AND ASSESSMENT COMPLIANCE RISKS**
- Evaluate internal audit program effectiveness and coverage
- Assess external audit and certification compliance requirements
- Analyze documentation and record-keeping compliance adequacy
- Review compliance monitoring and measurement program effectiveness
- Evaluate corrective action and continuous improvement processes

**SECTION 5: EMERGING COMPLIANCE RISKS**
- Assess impact of proposed regulatory changes and new legislation
- Evaluate cross-jurisdictional compliance complexity and conflicts
- Analyze technology-driven compliance challenges (AI, automation)
- Review ESG and sustainability reporting data protection implications
- Assess cybersecurity regulation compliance intersection with data protection

RISK SCORING CRITERIA:
- Likelihood: Very Low (1) to Very High (5)
- Impact: Minimal (1) to Catastrophic (5)
- Overall Risk: Low (1-6), Medium (7-15), High (16-25)

TARGET OUTPUT: Comprehensive compliance risk assessment (3,500-4,500 words)
FOCUS: Compliance risk mitigation priorities with regulatory alignment strategies.

RISK ASSESSMENT TASK: Conduct detailed compliance risk analysis based on collected data.
"""

        else:
            # Fallback for any other risk types
            return base_context + f"""
RISK ASSESSMENT REQUIREMENTS - {risk_type.upper()} RISK ANALYSIS:

**COMPREHENSIVE RISK EVALUATION**
- Conduct detailed {risk_type} risk assessment for {request.company_name}
- Analyze risks specific to {request.framework} compliance requirements
- Consider {request.industry} sector-specific risk factors
- Address {request.jurisdiction} regulatory risk environment
- Account for {request.risk_profile} organizational risk profile

**RISK ANALYSIS FRAMEWORK**
- Identify and categorize {risk_type} risks
- Assess likelihood and impact of identified risks
- Evaluate current controls and mitigation measures
- Recommend additional risk treatment strategies
- Prioritize risk mitigation actions based on severity and feasibility

RISK SCORING CRITERIA:
- Likelihood: Very Low (1) to Very High (5)
- Impact: Minimal (1) to Catastrophic (5)
- Overall Risk: Low (1-6), Medium (7-15), High (16-25)

TARGET OUTPUT: Comprehensive {risk_type} risk assessment (3,000-4,000 words)
FOCUS: Actionable risk mitigation recommendations with implementation priorities.

RISK ASSESSMENT TASK: Conduct comprehensive {risk_type} risk analysis based on collected data.
"""

    def get_assembly_prompt(self, framework_output: StageOutput, procedures_output: StageOutput, tools_output: StageOutput, request: PolicyRequest, data_context: Optional[StageOutput] = None, risk_context: Optional[StageOutput] = None) -> str:
        """Formats the document assembly prompt."""
        try:
            # Prepare variable values using the unified engine
            variable_values = self.variable_engine.prepare_variable_values(
                request=request,
                framework_output=framework_output,
                procedures_output=procedures_output,
                tools_output=tools_output,
                data_context=data_context,
                risk_context=risk_context
            )

            # Perform variable substitution
            return self.variable_engine.substitute_variables(self.ASSEMBLY_PROMPT, variable_values)

        except VariableValidationError as e:
            logger.error("Variable validation failed in get_assembly_prompt: %s", str(e))
            # Fallback to original method for backward compatibility
            company_context_details = [
                f"Company Name: {request.company_name}",
                f"Type: {request.company_type}",
                f"Industry: {request.industry}",
                f"Employees: {request.employee_count}",
                f"Jurisdiction: {request.jurisdiction}",
                f"Risk Profile: {request.risk_profile}",
                f"Processes Data Types: {', '.join(request.data_types)}",
                f"Existing Policies: {', '.join(request.existing_policies)}",
                f"Policy Complexity: {request.complexity_level}"
            ]
            company_context = ". ".join(filter(None, company_context_details)) + "."

            return self.ASSEMBLY_PROMPT.format(
                company_name=request.company_name,
                framework_content=framework_output.content,
                procedures_content=procedures_output.content,
                tools_content=tools_output.content,
                company_context=company_context,
                data_context=data_context.content if data_context else "No data context provided.",
                risk_context=risk_context.content if risk_context else "No risk context provided."
            )


class EnhancedPolicyGenerator:
    """Orchestrates the multi-stage generation of comprehensive compliance policies."""

    def __init__(self, db: AsyncIOMotorDatabase, ai_service: 'AIService'): # Forward reference AIService
        self.db = db
        self.ai_service = ai_service
        self.prompt_manager = PolicyPromptManager()
        self.quality_validator = PolicyQualityValidator()
        
        # Initialize enhanced infrastructure components
        self.error_recovery_manager = get_recovery_manager()
        
        # Initialize circuit breakers for external services
        self.ai_service_cb = create_ai_service_circuit_breaker(name="ai-service")
        
        self.db_service_cb = create_quick_circuit_breaker(name="database-service")
        
        # Initialize retry configurations
        self.ai_retry_config = AI_SERVICE_RETRY_CONFIG
        self.db_retry_config = QUICK_RETRY_CONFIG
        
        logger.info("EnhancedPolicyGenerator initialized with enterprise infrastructure")

    @with_langtrace_root_span(name="generate_comprehensive_policy")
    async def generate_comprehensive_policy(self, request: PolicyRequest) -> Optional[PolicyDocument]:
        """Generates a comprehensive policy document through a multi-stage process."""
        generation_id = str(uuid.uuid4())

        # Add trace metadata (using logger for now)
        logger.info("Starting comprehensive policy generation", extra={
            "company_name": request.company_name,
            "framework": request.framework,
            "complexity_level": request.complexity_level,
            "generation_id": generation_id,
            "data_types_count": len(request.data_types),
            "employee_count": request.employee_count
        })

        # Use correlation context as a context manager
        with correlation_context(
            correlation_id=generation_id,
            operation="generate_comprehensive_policy",
            stage="initialization",
            user_id=request.user_id
        ):
            # Enhanced structured logging with correlation context
            logger.info(
                "Starting comprehensive policy generation",
                extra={
                    "generation_id": generation_id,
                    "company_name": request.company_name,
                    "framework": request.framework,
                    "user_id": request.user_id,
                    "complexity_level": request.complexity_level,
                    "stage": "initialization"
                }
            )
            overall_start_time = time.monotonic()

            try:
                # Stage 1: Data Collection - Gather organization and regulatory information
                data_output = await self._collect_data(request, generation_id)
                if not data_output:
                    return None

                # Stage 2: Risk Assessment - Analyze potential compliance risks based on collected data
                risk_output = await self._assess_risks(data_output, request, generation_id)
                if not risk_output:
                    return None

                # Stage 3: Framework - Generate policy framework using data and risk context
                framework_output = await self._generate_framework(request, generation_id,
                                                                 data_context=data_output,
                                                                 risk_context=risk_output)
                if not framework_output:
                    logger.error(f"Policy generation {generation_id} failed: Framework generation failed and returned None")
                    return None

                # Stage 4: Procedures - Generate procedures based on framework
                procedures_output = await self._generate_procedures(framework_output, request, generation_id, data_context=data_output, risk_context=risk_output)
                if not procedures_output:
                    logger.error(f"Policy generation {generation_id} failed: Procedures generation failed and returned None")
                    return None

                # Stage 5: Tools - Generate tools and templates
                tools_output = await self._generate_tools(framework_output, procedures_output, request, generation_id, data_context=data_output, risk_context=risk_output)
                if not tools_output:
                    logger.error(f"Policy generation {generation_id} failed: Tools generation failed and returned None")
                    return None

                # Stage 6: Assembly - Assemble final policy document
                assembled_document_output = await self._assemble_document(framework_output, procedures_output, tools_output, request, generation_id, data_context=data_output, risk_context=risk_output)
                if not assembled_document_output:
                    logger.error(f"Policy generation {generation_id} failed at stage assembly: Document assembly failed and returned None")
                    return None

                # Calculate overall quality score using the core stages
                overall_quality_score = await self.quality_validator.calculate_overall_score(
                    framework_output, procedures_output, tools_output
                )

                # Calculate total word count from all stages
                total_words = (
                    (data_output.word_count if data_output else 0) +
                    (risk_output.word_count if risk_output else 0) +
                    (framework_output.word_count if framework_output else 0) +
                    (procedures_output.word_count if procedures_output else 0) +
                    (tools_output.word_count if tools_output else 0) +
                    (assembled_document_output.word_count if assembled_document_output else 0)
                )

                # Estimate total pages (e.g., 300 words per page)
                words_per_page = 300
                total_pages = math.ceil(total_words / words_per_page) if words_per_page > 0 else 0

                overall_generation_duration = round(time.monotonic() - overall_start_time, 2)

                policy_doc = PolicyDocument(
                    policy_id=generation_id,
                    content={
                        GenerationStage.DATA_COLLECTION.value: data_output,
                        GenerationStage.RISK_ASSESSMENT.value: risk_output,
                        GenerationStage.FRAMEWORK.value: framework_output,
                        GenerationStage.PROCEDURES.value: procedures_output,
                        GenerationStage.TOOLS.value: tools_output,
                        GenerationStage.ASSEMBLY.value: assembled_document_output
                    },
                    metadata={
                        "company_name": request.company_name,
                        "framework": request.framework,
                        "generated_at": datetime.now(timezone.utc).isoformat(),
                        "generation_id": generation_id,
                        "user_id": request.user_id,
                        "total_words": total_words,
                        "total_pages": total_pages,
                        "generation_time": overall_generation_duration
                    },
                    quality_metrics={
                        "overall_score": overall_quality_score,
                        "data_collection_score": data_output.completeness_score,
                        "risk_assessment_score": risk_output.completeness_score,
                        "framework_score": framework_output.completeness_score,
                        "procedures_score": procedures_output.completeness_score,
                        "tools_score": tools_output.completeness_score,
                        # Add assembly score if it's meaningful
                        "assembly_score": assembled_document_output.completeness_score
                    },
                    generation_config=request.__dict__ # Store the initial request parameters
                )

                await self._save_enhanced_policy(policy_doc, request)
                logger.info(f"[{generation_id}] Successfully generated and saved policy. Overall generation time: {time.monotonic() - overall_start_time:.2f}s")
                return policy_doc

            except Exception as e:
                # Centralized error handling for the entire process
                # The stage-specific error handlers might have already logged,
                # but this catches errors in the orchestration itself.
                logger.error(f"[{generation_id}] Critical error during comprehensive policy generation: {e}", exc_info=True)
                # Optionally, call _handle_generation_error if the error is specific to a stage not caught by inner try-excepts
                # For instance, if an error occurs outside _generate_X methods but within this main try block.
                # await self._handle_generation_error(e, request, generation_id, GenerationStage.ASSEMBLY) # Example, adjust stage as needed
                return None

    @metrics_collector.track_stage(StageType.DATA_COLLECTION)
    async def _collect_data(self, request: PolicyRequest, generation_id: str) -> Optional[StageOutput]:
        """Data collection stage - gather organization and regulatory information"""
        stage_name = GenerationStage.DATA_COLLECTION.value
        logger.info(f"[{generation_id}] Entered _collect_data for {request.company_name}")
        start_time = time.monotonic()
        try:
            # Initialize data collection tasks
            data_tasks = []
            
            # Task 1: Organizational data analysis
            @with_langtrace_root_span(name="collect_organizational_data")
            async def collect_organizational_data():
                logger.info("Starting organizational data collection", extra={
                    "stage": "data_collection",
                    "analysis_type": "organizational",
                    "company": request.company_name
                })
                org_prompt = self.prompt_manager.get_data_collection_prompt(request, "organizational")
                
                @metrics_collector.track_ai_service_call("gemini_api")
                @with_circuit_breaker(self.ai_service_cb) # Corrected decorator usage
                @with_retry_config(AI_SERVICE_RETRY_CONFIG)
                @with_recovery(
                    stage="data_collection_org",
                    max_attempts=AI_SERVICE_RETRY_CONFIG.max_attempts
                )
                async def protected_org_call():
                    content = await self.ai_service.generate_content(org_prompt)
                    if not content:
                        raise AIServiceException(
                            "AI service returned empty content for organizational data collection",
                            correlation_id=generation_id,
                            stage="data_collection_org"
                        )
                    return content
                
                return await protected_org_call()
            
            # Task 2: Regulatory environment analysis
            async def collect_regulatory_data():
                reg_prompt = self.prompt_manager.get_data_collection_prompt(request, "regulatory")
                
                @metrics_collector.track_ai_service_call("gemini_api")
                @with_circuit_breaker(self.ai_service_cb) # Corrected decorator usage
                @with_retry_config(AI_SERVICE_RETRY_CONFIG)
                @with_recovery(
                    stage="data_collection_reg",
                    max_attempts=AI_SERVICE_RETRY_CONFIG.max_attempts
                )
                async def protected_reg_call():
                    content = await self.ai_service.generate_content(reg_prompt)
                    if not content:
                        raise AIServiceException(
                            "AI service returned empty content for regulatory data collection",
                            correlation_id=generation_id,
                            stage="data_collection_reg"
                        )
                    return content
                
                return await protected_reg_call()
            
            # Task 3: Industry context analysis
            async def collect_industry_data():
                industry_prompt = self.prompt_manager.get_data_collection_prompt(request, "industry")
                
                @metrics_collector.track_ai_service_call("gemini_api")
                @with_circuit_breaker(self.ai_service_cb) # Corrected decorator usage
                @with_retry_config(AI_SERVICE_RETRY_CONFIG)
                @with_recovery(
                    stage="data_collection_industry",
                    max_attempts=AI_SERVICE_RETRY_CONFIG.max_attempts
                )
                async def protected_industry_call():
                    content = await self.ai_service.generate_content(industry_prompt)
                    if not content:
                        raise AIServiceException(
                            "AI service returned empty content for industry data collection",
                            correlation_id=generation_id,
                            stage="data_collection_industry"
                        )
                    return content
                
                return await protected_industry_call()
            
            # Execute data collection tasks in parallel for performance
            logger.debug(f"[{generation_id}:{stage_name}] Executing parallel data collection tasks")
            org_data, reg_data, industry_data = await asyncio.gather(
                collect_organizational_data(),
                collect_regulatory_data(),
                collect_industry_data(),
                return_exceptions=True
            )
            
            # Handle any exceptions from parallel execution
            # Handle any exceptions from parallel execution
            # The with_recovery decorator will return None if recovery fails,
            # so we just need to check for None here.
            if org_data is None:
                logger.error(f"[{generation_id}:{stage_name}] Organizational data collection failed or recovered with None.")
                return None
            
            if reg_data is None:
                logger.error(f"[{generation_id}:{stage_name}] Regulatory data collection failed or recovered with None.")
                return None
            
            if industry_data is None:
                logger.error(f"[{generation_id}:{stage_name}] Industry data collection failed or recovered with None.")
                return None
            
            # Combine collected data
            combined_data = f"""ORGANIZATIONAL DATA:
{org_data}

REGULATORY ENVIRONMENT:
{reg_data}

INDUSTRY CONTEXT:
{industry_data}"""
            
            word_count = len(combined_data.split())
            # Validate data collection completeness
            completeness_score = self.quality_validator.validate_data_collection(combined_data)
            
            output = StageOutput(
                content=combined_data,
                word_count=word_count,
                completeness_score=completeness_score,
                generation_time=time.monotonic() - start_time,
                metadata={
                    "org_data_length": len(org_data.split()) if isinstance(org_data, str) else 0,
                    "reg_data_length": len(reg_data.split()) if isinstance(reg_data, str) else 0,
                    "industry_data_length": len(industry_data.split()) if isinstance(industry_data, str) else 0,
                    "parallel_execution": True
                }
            )
            
            await self._log_stage_completion(GenerationStage.DATA_COLLECTION, output, generation_id, request.user_id)
            logger.info(f"[{generation_id}:{stage_name}] Data collection completed in {output.generation_time:.2f}s")
            return output
            
        except Exception as e:
            return await self._handle_generation_error(e, request, generation_id, GenerationStage.DATA_COLLECTION)

    @metrics_collector.track_stage(StageType.RISK_ASSESSMENT)
    async def _assess_risks(self, data_output: StageOutput, request: PolicyRequest, generation_id: str) -> Optional[StageOutput]:
        """Risk assessment stage - analyze potential compliance risks based on collected data"""
        stage_name = GenerationStage.RISK_ASSESSMENT.value
        logger.info(f"[{generation_id}] Entered _assess_risks for {request.company_name}")
        start_time = time.monotonic()
        try:
            # Initialize risk assessment tasks
            collected_data = data_output.content
            
            # Task 1: Technical risk assessment
            async def assess_technical_risks():
                tech_prompt = self.prompt_manager.get_risk_assessment_prompt(request, collected_data, "technical")
                
                @metrics_collector.track_ai_service_call("gemini_api")
                @with_circuit_breaker(self.ai_service_cb) # Corrected decorator usage
                @with_retry_config(AI_SERVICE_RETRY_CONFIG)
                @with_recovery(
                    stage="risk_assessment_tech",
                    max_attempts=AI_SERVICE_RETRY_CONFIG.max_attempts
                )
                async def protected_tech_risk_call():
                    content = await self.ai_service.generate_content(tech_prompt)
                    if not content:
                        raise AIServiceException(
                            "AI service returned empty content for technical risk assessment",
                            correlation_id=generation_id,
                            context={"stage": "risk_assessment_tech"}
                        )
                    return content
                
                return await protected_tech_risk_call()
            
            # Task 2: Operational risk assessment
            async def assess_operational_risks():
                ops_prompt = self.prompt_manager.get_risk_assessment_prompt(request, collected_data, "operational")
                
                @metrics_collector.track_ai_service_call("gemini_api")
                @with_circuit_breaker(self.ai_service_cb) # Corrected decorator usage
                @with_retry_config(AI_SERVICE_RETRY_CONFIG)
                @with_recovery(
                    stage="risk_assessment_ops",
                    max_attempts=AI_SERVICE_RETRY_CONFIG.max_attempts
                )
                async def protected_ops_risk_call():
                    content = await self.ai_service.generate_content(ops_prompt)
                    if not content:
                        raise AIServiceException(
                            "AI service returned empty content for operational risk assessment",
                            correlation_id=generation_id,
                            context={"stage": "risk_assessment_ops"}
                        )
                    return content
                
                return await protected_ops_risk_call()
            
            # Task 3: Compliance risk assessment
            async def assess_compliance_risks():
                compliance_prompt = self.prompt_manager.get_risk_assessment_prompt(request, collected_data, "compliance")
                
                @metrics_collector.track_ai_service_call("gemini_api")
                @with_circuit_breaker(self.ai_service_cb) # Corrected decorator usage
                @with_retry_config(AI_SERVICE_RETRY_CONFIG)
                @with_recovery(
                    stage="risk_assessment_compliance",
                    max_attempts=AI_SERVICE_RETRY_CONFIG.max_attempts
                )
                async def protected_compliance_risk_call():
                    content = await self.ai_service.generate_content(compliance_prompt)
                    if not content:
                        raise AIServiceException(
                            "AI service returned empty content for compliance risk assessment",
                            correlation_id=generation_id,
                            context={"stage": "risk_assessment_compliance"}
                        )
                    return content
                
                return await protected_compliance_risk_call()
            
            # Execute risk assessment tasks in parallel for performance
            logger.debug(f"[{generation_id}:{stage_name}] Executing parallel risk assessment tasks")
            tech_risks, ops_risks, compliance_risks = await asyncio.gather(
                assess_technical_risks(),
                assess_operational_risks(),
                assess_compliance_risks(),
                return_exceptions=True
            )
            
            # Handle any exceptions from parallel execution
            # Handle any exceptions from parallel execution
            if tech_risks is None:
                logger.error(f"[{generation_id}:{stage_name}] Technical risk assessment failed or recovered with None.")
                return None
            
            if ops_risks is None:
                logger.error(f"[{generation_id}:{stage_name}] Operational risk assessment failed or recovered with None.")
                return None
            
            if compliance_risks is None:
                logger.error(f"[{generation_id}:{stage_name}] Compliance risk assessment failed or recovered with None.")
                return None
            
            # Combine risk assessments
            combined_risks = f"""TECHNICAL RISKS:
{tech_risks}

OPERATIONAL RISKS:
{ops_risks}

COMPLIANCE RISKS:
{compliance_risks}"""
            
            word_count = len(combined_risks.split())
            # Validate risk assessment completeness
            completeness_score = self.quality_validator.validate_risk_assessment(combined_risks)
            
            output = StageOutput(
                content=combined_risks,
                word_count=word_count,
                completeness_score=completeness_score,
                generation_time=time.monotonic() - start_time,
                metadata={
                    "input_data_word_count": data_output.word_count,
                    "tech_risks_length": len(tech_risks.split()) if isinstance(tech_risks, str) else 0,
                    "ops_risks_length": len(ops_risks.split()) if isinstance(ops_risks, str) else 0,
                    "compliance_risks_length": len(compliance_risks.split()) if isinstance(compliance_risks, str) else 0,
                    "parallel_execution": True,
                    "data_collection_time": data_output.generation_time
                }
            )
            
            await self._log_stage_completion(GenerationStage.RISK_ASSESSMENT, output, generation_id, request.user_id)
            logger.info(f"[{generation_id}:{stage_name}] Risk assessment completed in {output.generation_time:.2f}s")
            return output
            
        except Exception as e:
            return await self._handle_generation_error(e, request, generation_id, GenerationStage.RISK_ASSESSMENT)

    @metrics_collector.track_stage(StageType.FRAMEWORK)
    async def _generate_framework(self, request: PolicyRequest, generation_id: str,
                                  data_context: Optional[StageOutput] = None,
                                  risk_context: Optional[StageOutput] = None) -> Optional[StageOutput]:
        """Generate legal framework stage with data and risk analysis context"""
        stage_name = GenerationStage.FRAMEWORK.value
        logger.info(f"[{generation_id}] Entered _generate_framework for {request.company_name}")
        start_time = time.monotonic()
        try:
            prompt = self.prompt_manager.get_framework_prompt(request, data_context, risk_context)
            logger.debug(f"[{generation_id}:{stage_name}] Prompt length: {len(prompt)}")

            # Protected AI service call with enterprise infrastructure
            @metrics_collector.track_ai_service_call("gemini_api")
            @with_circuit_breaker(self.ai_service_cb) # Corrected decorator usage
            @with_retry_config(AI_SERVICE_RETRY_CONFIG)
            @with_recovery(
                stage="framework",
                max_attempts=AI_SERVICE_RETRY_CONFIG.max_attempts
            )
            async def protected_ai_call():
                content = await self.ai_service.generate_content(prompt)
                if not content:
                    raise AIServiceException(
                        "AI service returned empty content for framework generation",
                        correlation_id=generation_id,
                        context={"stage": "framework"}
                    )
                return content
            
            ai_content = await protected_ai_call()

            word_count = len(ai_content.split())
            # Validate content quality
            completeness_score = await self.quality_validator.validate_framework(ai_content)
            
            output = StageOutput(
                content=ai_content,
                word_count=word_count,
                completeness_score=completeness_score,
                generation_time=time.monotonic() - start_time,
                metadata={"prompt_length": len(prompt)}
            )
            await self._log_stage_completion(GenerationStage.FRAMEWORK, output, generation_id, request.user_id)
            return output
        except Exception as e:
            return await self._handle_generation_error(e, request, generation_id, GenerationStage.FRAMEWORK)

    @metrics_collector.track_stage(StageType.PROCEDURES)
    async def _generate_procedures(self, framework_output: StageOutput, request: PolicyRequest, generation_id: str, data_context: Optional[StageOutput] = None, risk_context: Optional[StageOutput] = None) -> Optional[StageOutput]:
        """Generate operational procedures stage"""
        stage_name = GenerationStage.PROCEDURES.value
        logger.info(f"[{generation_id}] Entered _generate_procedures for {request.company_name}")
        start_time = time.monotonic()
        try:
            prompt = self.prompt_manager.get_procedures_prompt(framework_output, request, data_context, risk_context)
            logger.debug(f"[{generation_id}:{stage_name}] Prompt length: {len(prompt)}")

            # Protected AI service call with enterprise infrastructure
            @metrics_collector.track_ai_service_call("gemini_api")
            @with_circuit_breaker(self.ai_service_cb) # Corrected decorator usage
            @with_retry_config(AI_SERVICE_RETRY_CONFIG)
            @with_recovery(
                stage="procedures",
                max_attempts=AI_SERVICE_RETRY_CONFIG.max_attempts
            )
            async def protected_ai_call():
                content = await self.ai_service.generate_content(prompt)
                if not content:
                    raise AIServiceException(
                        "AI service returned empty content for procedures generation",
                        correlation_id=generation_id,
                        context={"stage": "procedures"}
                    )
                return content
            
            ai_content = await protected_ai_call()

            word_count = len(ai_content.split())
            completeness_score = await self.quality_validator.validate_procedures(ai_content)

            output = StageOutput(
                content=ai_content,
                word_count=word_count,
                completeness_score=completeness_score,
                generation_time=time.monotonic() - start_time,
                metadata={"prompt_length": len(prompt), "framework_word_count": framework_output.word_count}
            )
            await self._log_stage_completion(GenerationStage.PROCEDURES, output, generation_id, request.user_id)
            return output
        except Exception as e:
            return await self._handle_generation_error(e, request, generation_id, GenerationStage.PROCEDURES)

    async def _generate_tools(self, framework_output: StageOutput, procedures_output: StageOutput, request: PolicyRequest, generation_id: str, data_context: Optional[StageOutput] = None, risk_context: Optional[StageOutput] = None) -> Optional[StageOutput]:
        """Generate tools and templates stage"""
        stage_name = GenerationStage.TOOLS.value
        logger.info(f"[{generation_id}] Entered _generate_tools for {request.company_name}")
        start_time = time.monotonic()
        try:
            prompt = self.prompt_manager.get_tools_prompt(framework_output, procedures_output, request, data_context, risk_context)
            logger.debug(f"[{generation_id}:{stage_name}] Prompt length: {len(prompt)}")

            # Protected AI service call with enterprise infrastructure
            @metrics_collector.track_ai_service_call("gemini_api")
            @with_circuit_breaker(self.ai_service_cb) # Corrected decorator usage
            @with_retry_config(AI_SERVICE_RETRY_CONFIG)
            @with_recovery(
                stage="tools",
                max_attempts=AI_SERVICE_RETRY_CONFIG.max_attempts
            )
            async def protected_ai_call():
                content = await self.ai_service.generate_content(prompt)
                if not content:
                    raise AIServiceException(
                        "AI service returned empty content for tools generation",
                        correlation_id=generation_id,
                        context={"stage": "tools"}
                    )
                return content
            
            ai_content = await protected_ai_call()

            word_count = len(ai_content.split())
            completeness_score = await self.quality_validator.validate_tools(ai_content)

            generation_time = time.monotonic() - start_time
            logger.info(f"[{generation_id}:{stage_name}] Content generated. Word count: {word_count}, Score: {completeness_score:.2f}, Time: {generation_time:.2f}s")

            output = StageOutput(
                content=ai_content,
                word_count=word_count,
                completeness_score=completeness_score,
                generation_time=round(generation_time, 2),
                metadata={"prompt_chars": len(prompt), "model_name": self.ai_service.model.model_name if hasattr(self.ai_service, 'model') else 'unknown'}
            )
            await self._log_stage_completion(GenerationStage.TOOLS, output, generation_id, request.user_id)
            return output
        except Exception as e:
            logger.error(f"[{generation_id}:{stage_name}] Exception: {e}", exc_info=True)
            raise

    @metrics_collector.track_stage(StageType.ASSEMBLY)
    async def _assemble_document(self, framework_output: StageOutput, procedures_output: StageOutput, tools_output: StageOutput, request: PolicyRequest, generation_id: str, data_context: Optional[StageOutput] = None, risk_context: Optional[StageOutput] = None) -> Optional[StageOutput]:
        """Assemble final policy document from all stage outputs using an AI-driven assembly process."""
        stage_name = GenerationStage.ASSEMBLY.value
        logger.info(f"[{generation_id}] Entered _assemble_document for {request.company_name}")
        start_time = time.monotonic()
        try:
            prompt = self.prompt_manager.get_assembly_prompt(framework_output, procedures_output, tools_output, request, data_context=data_context, risk_context=risk_context)
            logger.debug(f"[{generation_id}:{stage_name}] Prompt length: {len(prompt)}")

            @metrics_collector.track_ai_service_call("gemini_api")
            @with_circuit_breaker(self.ai_service_cb) # Corrected decorator usage
            @with_retry_config(AGGRESSIVE_RETRY_CONFIG)
            @with_recovery(
                stage="assembly",
                max_attempts=AGGRESSIVE_RETRY_CONFIG.max_attempts
            )
            async def protected_ai_call():
                content = await self.ai_service.generate_content(prompt, max_tokens=8192) # Use max tokens for assembly
                if not content:
                    raise AIServiceException(
                        "AI service returned empty content for document assembly",
                        correlation_id=generation_id,
                        context={"stage": "assembly"}
                    )
                return content
            
            assembled_content = await protected_ai_call()

            word_count = len(assembled_content.split())
            completeness_score = await self.quality_validator.validate_assembly(assembled_content)
            
            generation_time = time.monotonic() - start_time
            logger.info(f"[{generation_id}:{stage_name}] Content assembled. Word count: {word_count}, Score: {completeness_score:.2f}, Time: {generation_time:.2f}s")

            output = StageOutput(
                content=assembled_content,
                word_count=word_count,
                completeness_score=completeness_score,
                generation_time=round(generation_time, 2),
                metadata={"assembly_details": "AI-driven assembly from 5 stages", "prompt_length": len(prompt)}
            )
            await self._log_stage_completion(GenerationStage.ASSEMBLY, output, generation_id, request.user_id)
            return output
        except Exception as e:
            return await self._handle_generation_error(e, request, generation_id, GenerationStage.ASSEMBLY)

    async def _log_stage_completion(self, stage: GenerationStage, output: StageOutput, generation_id: str, user_id: str):
        """Log stage completion for monitoring"""
        # Ensure output.metadata is not None and handle potential missing generation_id if necessary
        # Though generation_id is passed as a direct argument now, so output.metadata.get("generation_id") might be redundant or for cross-checking
        log_entry = {
            "user_id": user_id,
            "stage": stage.value, # Use stage.value for the string representation
            "generation_id": generation_id, # Use the direct argument
            "word_count": output.word_count,
            "completeness_score": output.completeness_score,
            "generation_time_seconds": output.generation_time,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "target_achieved": output.completeness_score >= 0.90, # Example target
            "model_name": output.metadata.get("model_name", "unknown") if output.metadata else "unknown",
            "prompt_chars": output.metadata.get("prompt_chars") if output.metadata else None
        }
        try:
            await self.db.generation_logs.insert_one(log_entry)
            logger.info(f"[{generation_id}:{stage.value}] Stage completed and logged. Words: {output.word_count}, Score: {output.completeness_score:.2f}, Time: {output.generation_time:.2f}s")
        except Exception as e:
            logger.error(f"[{generation_id}:{stage.value}] Failed to log stage completion to DB: {e}", exc_info=True)
    
    async def _save_enhanced_policy(self, policy: PolicyDocument, request: PolicyRequest):
        """Save enhanced policy to database"""
        policy_data = {
            "id": policy.policy_id,
            "user_id": request.user_id,
            "title": f"{request.framework} Policy for {request.company_name}",
            "framework": request.framework,
            "company_name": request.company_name,
            "industry": request.industry,
            
            # Enhanced content structure
            "content_sections": {
                "framework": {
                    "content": policy.content[GenerationStage.FRAMEWORK.value].content,
                    "word_count": policy.content[GenerationStage.FRAMEWORK.value].word_count,
                    "completeness_score": policy.content[GenerationStage.FRAMEWORK.value].completeness_score
                },
                "procedures": {
                    "content": policy.content[GenerationStage.PROCEDURES.value].content,
                    "word_count": policy.content[GenerationStage.PROCEDURES.value].word_count,
                    "completeness_score": policy.content[GenerationStage.PROCEDURES.value].completeness_score
                },
                "tools": {
                    "content": policy.content[GenerationStage.TOOLS.value].content,
                    "word_count": policy.content[GenerationStage.TOOLS.value].word_count,
                    "completeness_score": policy.content[GenerationStage.TOOLS.value].completeness_score
                },
                "assembled": {
                    "content": policy.content[GenerationStage.ASSEMBLY.value].content,
                    "word_count": policy.content[GenerationStage.ASSEMBLY.value].word_count,
                    "completeness_score": policy.content[GenerationStage.ASSEMBLY.value].completeness_score
                }
            },
            
            # Enhanced metadata
            "metadata": policy.metadata,
            "quality_metrics": policy.quality_metrics,
            "generation_config": policy.generation_config,
            
            # Standard fields
            "ai_generated": True,
            "version": 1,
            "status": "draft",
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }
        
        await self.db.policies.insert_one(policy_data)
    
    async def _handle_generation_error(self, error: Exception, request: PolicyRequest, generation_id: str, stage: GenerationStage):
        """Handle generation errors with comprehensive logging"""
        error_data = {
            "generation_id": generation_id,
            "user_id": request.user_id,
            "company_name": request.company_name,
            "framework": request.framework,
            "stage": stage.value,  # Use passed stage parameter
            "error_type": type(error).__name__,
            "error_message": str(error),
            "timestamp": datetime.now(timezone.utc)
        }
        
        await self.db.generation_errors.insert_one(error_data)
        logger.error(f"Policy generation {generation_id} failed at stage {stage.value}: {error}", exc_info=True) # Use passed stage, add generation_id and exc_info
        return None  # Explicitly return None

class AIService:
    """AI service wrapper for Gemini integration"""
    
    def __init__(self, api_key: str):
        genai.configure(api_key=api_key)
        self.model = genai.GenerativeModel("gemini-2.5-flash-preview-05-20")
        
    @with_langtrace_root_span(name="ai_service_generate_content")
    async def generate_content(self, prompt: str, max_tokens: int = 8000) -> str:
        """Generate content using Gemini"""

        # Add trace metadata (using logger for now)
        logger.info("AI service generating content", extra={
            "model": "gemini-2.5-flash-preview-05-20",
            "max_tokens": max_tokens,
            "prompt_length": len(prompt),
            "prompt_preview": prompt[:200] + "..." if len(prompt) > 200 else prompt
        })

        generation_config = {
            "temperature": 0.3,
            "top_p": 0.8,
            "top_k": 40,
            "max_output_tokens": max_tokens,
        }
        
        try:
            logger.debug(f"AIService: Attempting to generate content with Gemini. Prompt length: {len(prompt)}")
            response = await asyncio.to_thread(
                self.model.generate_content,
                prompt,
                generation_config=generation_config
            )
            
            # Check if the response has text and if it's non-empty
            if hasattr(response, 'text') and response.text:
                logger.debug(f"AIService: Gemini content generated successfully. Response text length: {len(response.text)}")
                return response.text
            else:
                logger.error(f"AIService: Gemini API call succeeded but returned no text or an empty response. Full response: {response}")
                # Try to get parts if available, to understand what happened
                if hasattr(response, 'parts'):
                    logger.error(f"AIService: Gemini response parts: {response.parts}")
                if hasattr(response, 'prompt_feedback'):
                    logger.error(f"AIService: Gemini prompt feedback: {response.prompt_feedback}")
                return "" # Return empty string to signify failure to get content
                
        except Exception as e:
            logger.error(f"AIService: Exception during Gemini API call in AIService.generate_content: {str(e)}", exc_info=True)
            # Return empty string to match the "no content" path in _generate_framework
            return ""
